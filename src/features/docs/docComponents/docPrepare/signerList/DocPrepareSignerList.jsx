import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "semantic-ui-react";
import {
  changeEditMode,
  changeSelectedSigner,
} from "../../../../../app/annots/annotSlice";
import { openModal } from "../../../../../app/common/modals/modalSlice";
import { roleIsOtherAgentsClients } from "../sendForSigning/sendForSigningUtils";
import DocPrepareSignerListItem from "./DocPrepareSignerListItem";

export default function DocPrepareSignerList() {
  const dispatch = useDispatch();
  const { signerListDisplay, signerListPossible } = useSelector(
    (state) => state.annot
  );
  const { currentUserProfile } = useSelector((state) => state.profile);
  const { editMode } = useSelector((state) => state.annot);
  const { transaction } = useSelector((state) => state.transaction);

  function handleAddSigner() {
    dispatch(
      openModal({
        modalType: "DocPrepareAddSigner",
      })
    );
  }

  function handleYourFieldsClick() {
    dispatch(changeSelectedSigner(currentUserProfile));
    dispatch(changeEditMode("agent"));
  }

  const annotColor = "200, 200, 200";

  // Separate current agent's clients from other party clients
  const currentAgentClients = signerListDisplay.filter(
    (signer) => !roleIsOtherAgentsClients(signer.role, transaction)
  );

  const otherPartyClients = signerListPossible.filter(
    (signer) =>
      roleIsOtherAgentsClients(signer.role, transaction) &&
      signer.firstName &&
      signer.lastName // Only show if they have names
  );

  // Determine the title for other party clients
  const otherPartyTitle =
    transaction.agentRepresents === "Buyer" ? "Seller" : "Buyer";

  return (
    <>
      {" "}
      <div
        onClick={() => handleYourFieldsClick()}
        style={{
          display: "flex",
          alignItems: "center",
          cursor: "pointer",
        }}
      >
        <Icon
          className="small right margin"
          name={
            editMode === "agent" ? "square check outline" : "square outline"
          }
          size="large"
        />
        <div style={{ width: "20px", height: "15px" }}>
          <div
            className="annot-outer-wrapper"
            style={{
              backgroundColor: `rgba(${annotColor}, 0.2)`,
            }}
          >
            <div
              className="annot-inner-wrapper"
              style={{
                boxShadow: `rgb(${annotColor}) 0px 0px 0px 1px`,
                zIndex: 5,
              }}
            ></div>
          </div>
        </div>
        <h5
          className="zero top margin small left margin"
          style={{ color: "#666" }}
        >
          Me:&nbsp;
          {currentUserProfile.firstName + " " + currentUserProfile.lastName}
        </h5>
      </div>
      <br />
      {currentAgentClients.map((client, index) => (
        <DocPrepareSignerListItem
          signer={client}
          key={client.role}
          index={index}
        />
      ))}
      <Button basic size="tiny" onClick={() => handleAddSigner()}>
        Add Signer
      </Button>
      {otherPartyClients.length > 0 && (
        <>
          <br />
          <Header
            as="h5"
            style={{ color: "#666", marginTop: "20px", marginBottom: "10px" }}
          >
            {otherPartyTitle} Clients:
          </Header>
          {otherPartyClients.map((client, index) => (
            <div key={client.role} style={{ opacity: 0.7 }}>
              <DocPrepareSignerListItem
                signer={client}
                index={index}
                isOtherParty={true}
              />
            </div>
          ))}
        </>
      )}
    </>
  );
}
